/*
 * Device LED Control Header
 * LED GPIO control and blink functionality
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /* LED状态定义 */
    typedef enum
    {
        LED_STATE_OFF = 1, /* LED关闭 */
        LED_STATE_ON = 0   /* LED开启 */
    } led_state_t;

    /* 电源来源状态定义 */
    typedef enum
    {
        POWER_SOURCE_EXTERNAL = 0, /* 外部供电（低电平） */
        POWER_SOURCE_BATTERY = 1   /* 电池供电（高电平） */
    } power_source_t;

    /* 充电状态定义 */
    typedef enum
    {
        CHARGE_STATUS_COMPLETE = 0, /* 充电完成（低电平） */
        CHARGE_STATUS_CHARGING = 1  /* 充电中（高电平） */
    } charge_status_t;

    /**
     * @brief 初始化LED GPIO

     *
     * @return
     *     - ESP_OK: 初始化成功
     *     - 其他: 初始化失败的错误码
     */
    esp_err_t led_charge_init(void);

    /**
     * @brief 创建LED闪烁任务
     *
     * 创建一个FreeRTOS任务，让LED以指定间隔闪烁
     *
     * @param task_priority 任务优先级
     * @return
     *     - ESP_OK: 任务创建成功
     *     - ESP_FAIL: 任务创建失败
     */
    esp_err_t led_charge_task();

    /**
     * @brief 读取电源来源状态
     *
     * @return power_source_t 电源来源状态
     */
    power_source_t get_power_source_status(void);

    /**
     * @brief 读取充电状态
     *
     * @return charge_status_t 充电状态
     */
    charge_status_t get_charge_status(void);

#ifdef __cplusplus
}
#endif