/*
 * 软件版本信息实现
 */

#include <stdio.h>
#include "esp_log.h"
#include "esp_system.h"
#include "esp_chip_info.h"
#include "esp_flash.h"
#include "version.h"
#include "dev_id.h"

static const char *TAG = "VERSION";

void print_version_info(void)
{
    esp_chip_info_t chip_info;
    uint32_t flash_size;
    uint16_t device_id = 0;
    uint8_t device_mac[6] = {0};
    
    esp_chip_info(&chip_info);
    esp_flash_get_size(NULL, &flash_size);

    ESP_LOGI(TAG, "========================================");
    ESP_LOGI(TAG, "  %s", PROJECT_NAME);
    ESP_LOGI(TAG, "========================================");
    ESP_LOGI(TAG, "Software Version: %s", SOFTWARE_VERSION_STRING);
    ESP_LOGI(TAG, "Build Date: %s %s", BUILD_DATE, BUILD_TIME);
    ESP_LOGI(TAG, "----------------------------------------");
    
    // 获取并打印设备唯一信息
    esp_err_t ret = get_device_id(&device_id);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "Device ID: %d", device_id);
    }
    else
    {
        ESP_LOGW(TAG, "Failed to get device ID: %s", esp_err_to_name(ret));
    }
    
    ret = get_device_mac(device_mac);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "Device MAC (Unique ID): %02X:%02X:%02X:%02X:%02X:%02X",
                 device_mac[0], device_mac[1], device_mac[2],
                 device_mac[3], device_mac[4], device_mac[5]);
    }
    else
    {
        ESP_LOGW(TAG, "Failed to get device MAC: %s", esp_err_to_name(ret));
    }
    
    ESP_LOGI(TAG, "----------------------------------------");
    ESP_LOGI(TAG, "Hardware Information:");
    ESP_LOGI(TAG, "  CPU Cores: %d", chip_info.cores);
    ESP_LOGI(TAG, "  Flash Size: %lu MB", flash_size / (1024 * 1024));
    ESP_LOGI(TAG, "  Chip Features: %s%s%s%s",
             (chip_info.features & CHIP_FEATURE_WIFI_BGN) ? "WiFi/" : "",
             (chip_info.features & CHIP_FEATURE_BT) ? "BT" : "",
             (chip_info.features & CHIP_FEATURE_BLE) ? "/BLE" : "",
             (chip_info.features & CHIP_FEATURE_IEEE802154) ? "/802.15.4" : "");
    ESP_LOGI(TAG, "----------------------------------------");
}

const char* get_version_string(void)
{
    return SOFTWARE_VERSION_STRING;
}

const char* get_build_info(void)
{
    static char build_info[128];
    snprintf(build_info, sizeof(build_info), 
             "%s (Built: %s %s)", 
             SOFTWARE_VERSION_STRING, BUILD_DATE, BUILD_TIME);
    return build_info;
}
