#include "led_charge.h"
#include "esp_log.h"

#define GPIO_CHARGE_LED GPIO_NUM_7     // LED GPIO引脚 (默认GPIO2)
#define GPIO_POWER_SOURCE GPIO_NUM_10  // 供电来源 -> 低电平:外部供电   高电平:电池供电
#define GPIO_CHARGE_STATUS GPIO_NUM_18 // 显示充电状态 -> 闪烁:充电中  常亮:充电完成
#define BLINK_INTERVAL_MS 500          // 闪烁间隔 (毫秒)

static const char *TAG = "LED_CHARGE";
static bool led_initialized = false;
static led_state_t current_led_state = LED_STATE_OFF;
static TaskHandle_t charge_task_handle = NULL;

esp_err_t led_charge_init(void)
{
    if (led_initialized)
    {
        ESP_LOGW(TAG, "LED already initialized");
        return ESP_OK;
    }

    // 配置LED GPIO为输出模式
    gpio_config_t led_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,            // 禁用中断
        .mode = GPIO_MODE_OUTPUT,                  // 输出模式
        .pin_bit_mask = (1ULL << GPIO_CHARGE_LED), // GPIO_CHARGE_LED
        .pull_down_en = 0,                         // 禁用下拉
        .pull_up_en = 0,                           // 禁用上拉
    };

    esp_err_t ret = gpio_config(&led_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "LED GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 配置电源来源GPIO为输入模式
    gpio_config_t power_source_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,              // 禁用中断
        .mode = GPIO_MODE_INPUT,                     // 输入模式
        .pin_bit_mask = (1ULL << GPIO_POWER_SOURCE), // GPIO_POWER_SOURCE
        .pull_down_en = 0,                           // 禁用下拉
        .pull_up_en = 1,                             // 启用上拉
    };

    ret = gpio_config(&power_source_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Power source GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 配置充电状态GPIO为输入模式
    gpio_config_t charge_status_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,               // 禁用中断
        .mode = GPIO_MODE_INPUT,                      // 输入模式
        .pin_bit_mask = (1ULL << GPIO_CHARGE_STATUS), // GPIO_CHARGE_STATUS
        .pull_down_en = 0,                            // 禁用下拉
        .pull_up_en = 1,                              // 启用上拉
    };

    ret = gpio_config(&charge_status_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Charge status GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始状态设为低电平（LED关闭）
    ret = gpio_set_level(GPIO_CHARGE_LED, LED_STATE_OFF);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set initial LED state: %s", esp_err_to_name(ret));
        return ret;
    }
    current_led_state = LED_STATE_OFF;

    led_initialized = true;
    ESP_LOGI(TAG, "GPIO%d initialized as LED output", GPIO_CHARGE_LED);
    ESP_LOGI(TAG, "GPIO%d initialized as power source input", GPIO_POWER_SOURCE);
    ESP_LOGI(TAG, "GPIO%d initialized as charge status input", GPIO_CHARGE_STATUS);

    return ESP_OK;
}

/**
 * @brief 读取电源来源状态
 */
power_source_t get_power_source_status(void)
{
    int level = gpio_get_level(GPIO_POWER_SOURCE);
    return (level == 0) ? POWER_SOURCE_EXTERNAL : POWER_SOURCE_BATTERY;
}

/**
 * @brief 读取充电状态
 */
charge_status_t get_charge_status(void)
{
    int level = gpio_get_level(GPIO_CHARGE_STATUS);
    return (level == 0) ? CHARGE_STATUS_COMPLETE : CHARGE_STATUS_CHARGING;
}

/**
 * @brief 显示充电状态
 */
static void led_charge_task_callback(void *pvParameters)
{
    while (1)
    {
        // 读取GPIO状态
        power_source_t power_source = get_power_source_status();
        charge_status_t charge_status = get_charge_status();

        // 根据需求实现LED控制逻辑
        // 当GPIO_POWER_SOURCE为低电平（外部供电）时：
        if (power_source == POWER_SOURCE_EXTERNAL)
        {
            // 如果GPIO_CHARGE_STATUS为低电平（充电完成）：LED常亮
            if (charge_status == CHARGE_STATUS_COMPLETE)
            {
                gpio_set_level(GPIO_CHARGE_LED, LED_STATE_ON);
                current_led_state = LED_STATE_ON;
                ESP_LOGI(TAG, "External power, charge complete - LED ON");
            }
            // 否则（充电中）：LED闪烁
            else
            {
                // 切换LED状态实现闪烁
                current_led_state = (current_led_state == LED_STATE_ON) ? LED_STATE_OFF : LED_STATE_ON;
                gpio_set_level(GPIO_CHARGE_LED, current_led_state);
                ESP_LOGI(TAG, "External power, charging - LED BLINK");
            }
        }
        // 当GPIO_POWER_SOURCE为高电平（电池供电）时：LED关闭
        else
        {
            gpio_set_level(GPIO_CHARGE_LED, LED_STATE_OFF);
            current_led_state = LED_STATE_OFF;
            ESP_LOGI(TAG, "Battery power - LED OFF");
        }

        // 延时
        vTaskDelay(pdMS_TO_TICKS(BLINK_INTERVAL_MS));
    }
}

/**
 * @brief 创建充电状态任务
 */
esp_err_t led_charge_task()
{
    if (!led_initialized)
    {
        ESP_LOGE(TAG, "led_charge not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (charge_task_handle != NULL)
    {
        ESP_LOGW(TAG, "led_charge task already running");
        return ESP_OK;
    }

    BaseType_t task_ret = xTaskCreate(
        led_charge_task_callback, // 任务函数
        "led_charge_task",        // 任务名称
        2048,                     // 堆栈大小
        NULL,                     // 任务参数
        5,                        // 任务优先级 (默认值)
        &charge_task_handle       // 任务句柄
    );

    if (task_ret != pdPASS)
    {
        ESP_LOGE(TAG, "failed to create led_charge task");
        charge_task_handle = NULL;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "led_charge task started with %d ms interval", BLINK_INTERVAL_MS);
    return ESP_OK;
}