
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"

#include "ble_init.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"
#include "local_ota.h"
#include "led_charge.h"
#include "dev_id.h"
#include "dev_power.h"
#include "version.h"
#include "sensor_test.h"

/* Base path for file server */
#define BASE_PATH "/data"

//
#define TAG "[SLAVE]"

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ret = nvs_flash_erase();
        ESP_ERROR_CHECK(ret);
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化设备ID模块 - 需要在打印版本信息之前初始化
    ret = dev_id_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize device ID: %s", esp_err_to_name(ret));
        return;
    }

    // 打印版本信息 - 现在可以显示设备MAC地址
    print_version_info();

    /*
    // 初始化BLE模块
    ret = ble_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize BLE: %s", esp_err_to_name(ret));
        return;
    }

    // 挂载文件系统
    ret = mount_storage(BASE_PATH);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "挂载文件系统失败: %s", esp_err_to_name(ret));
        return;
    }

    // 初始化本地OTA控制 (GPIO15控制WiFi AP和文件服务器)
    ret = local_ota_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize local OTA: %s", esp_err_to_name(ret));
        return;
    }

       // 初始化电源模块（按键和使能引脚）
       ret = dev_power_init();
       if (ret != ESP_OK) {
           ESP_LOGE(TAG, "Failed to initialize power: %s", esp_err_to_name(ret));
           return;
       }
       */


    // 初始化LED充电监控模块
    ret = led_charge_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "failed to initialize led charge: %s", esp_err_to_name(ret));
        return;
    }

     // 启动LED充电监控任务
    ret = led_charge_task();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start LED charge task: %s", esp_err_to_name(ret));
        return;
    }

    // 初始化传感器测试模块
    ret = sensor_test_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize sensor test: %s", esp_err_to_name(ret));
        return;
    }

    // 启动传感器测试任务
    ret = sensor_test_start_task();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start sensor test task: %s", esp_err_to_name(ret));
        return;
    }

    ESP_LOGI(TAG, "System initialization completed");
}
