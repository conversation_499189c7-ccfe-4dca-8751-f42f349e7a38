#include "dev_id.h"
#include "sdkconfig.h"
#include "esp_log.h"
#include "esp_efuse.h"
#include "esp_mac.h"
#include "nvs_flash.h"
#include "nvs.h"

static const char *TAG = "DEV_ID";

/* 全局变量存储设备信息 */
static bool dev_id_initialized = false;
static uint16_t cached_device_id = 0xFFFF;
static uint8_t cached_device_mac[DEV_MAC_ADDR_LEN] = {0};

/**
 * @brief 内部函数：从NVS读取设备ID
 */
static esp_err_t read_device_id_from_nvs(uint16_t *device_id)
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("device_config", NVS_READONLY, &nvs_handle);
    *device_id = 0xffff; // Set default device ID
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Unable to open NVS storage: %s, using default device ID: %d", esp_err_to_name(err), *device_id);
        return ESP_FAIL;
    }
    // First run: Write default ID to NVS after read failure
    // Subsequent runs: Read previously saved ID from NVS
    // After reflashing: Write default ID to NVS again
    err = nvs_get_u16(nvs_handle, "device_id", device_id);
    if (err != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to read device ID: %s, writing default device ID: %d", esp_err_to_name(err), *device_id);

        // First run or after reflashing, write default device ID to NVS
        nvs_close(nvs_handle);
        err = nvs_open("device_config", NVS_READWRITE, &nvs_handle);
        if (err == ESP_OK)
        {
            err = nvs_set_u16(nvs_handle, "device_id", *device_id);
            if (err == ESP_OK)
            {
                err = nvs_commit(nvs_handle);
                ESP_LOGI(TAG, "Default device ID saved to NVS: %d", *device_id);
            }
            else
            {
                ESP_LOGE(TAG, "Failed to save device ID: %s", esp_err_to_name(err));
            }
        }
    }
    nvs_close(nvs_handle);
    return ESP_OK; // Always return success as we provide default value
}

/**
 * @brief 内部函数：从eFuse读取MAC地址
 */
static esp_err_t read_device_mac_from_efuse(uint8_t *device_mac)
{
    esp_err_t err = esp_efuse_mac_get_default(device_mac);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to get base MAC address: %s", esp_err_to_name(err));
    }
    return err;
}

// 公共API函数实现
esp_err_t dev_id_init(void)
{
    if (dev_id_initialized) {
        ESP_LOGW(TAG, "Device ID module already initialized");
        return ESP_OK;
    }

    esp_err_t ret;

    // Read device ID
    ret = read_device_id_from_nvs(&cached_device_id);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Device ID read successfully: %d", cached_device_id);
    } else {
        ESP_LOGW(TAG, "Failed to read device ID, using default value: %d", cached_device_id);
    }

    // Read MAC address
    ret = read_device_mac_from_efuse(cached_device_mac);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Device MAC read successfully: %02X:%02X:%02X:%02X:%02X:%02X",
                 cached_device_mac[0], cached_device_mac[1], cached_device_mac[2],
                 cached_device_mac[3], cached_device_mac[4], cached_device_mac[5]);
    } else {
        ESP_LOGE(TAG, "Failed to read device MAC: %s", esp_err_to_name(ret));
        return ret;
    }

    dev_id_initialized = true;
    ESP_LOGI(TAG, "Device ID module initialized successfully");
    return ESP_OK;
}

esp_err_t get_device_id(uint16_t *device_id)
{
    if (!dev_id_initialized) {
        ESP_LOGE(TAG, "Device ID module not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (device_id == NULL) {
        ESP_LOGE(TAG, "Invalid parameter: device_id is NULL");
        return ESP_ERR_INVALID_ARG;
    }

    *device_id = cached_device_id;
    return ESP_OK;
}

esp_err_t get_device_mac(uint8_t *device_mac)
{
    if (!dev_id_initialized) {
        ESP_LOGE(TAG, "Device ID module not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (device_mac == NULL) {
        ESP_LOGE(TAG, "Invalid parameter: device_mac is NULL");
        return ESP_ERR_INVALID_ARG;
    }

    for (int i = 0; i < DEV_MAC_ADDR_LEN; i++) {
        device_mac[i] = cached_device_mac[i];
    }
    return ESP_OK;
}

esp_err_t set_device_id(uint16_t device_id)
{
    if (!dev_id_initialized) {
        ESP_LOGE(TAG, "Device ID module not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 验证设备ID范围 (1-255)
    if (device_id < 1 || device_id > 255) {
        ESP_LOGE(TAG, "Invalid device ID: %d (must be 1-255)", device_id);
        return ESP_ERR_INVALID_ARG;
    }

    // 打开NVS存储
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("device_config", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS storage: %s", esp_err_to_name(err));
        return ESP_FAIL;
    }

    // 保存新的设备ID到NVS
    err = nvs_set_u16(nvs_handle, "device_id", device_id);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to save device ID to NVS: %s", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return ESP_FAIL;
    }

    // 提交更改
    err = nvs_commit(nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to commit NVS changes: %s", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return ESP_FAIL;
    }

    nvs_close(nvs_handle);

    // 更新缓存的设备ID
    cached_device_id = device_id;

    ESP_LOGI(TAG, "Device ID successfully updated to: %d", device_id);
    return ESP_OK;
}

