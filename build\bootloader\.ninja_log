# ninja log v6
29	157	7776706507074264	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	b8ba88626490e6d2
34	241	7776706507134283	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	25e11b047edc83c0
60	300	7776706507384293	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	649cdad656f8b4f8
12	354	7776706506904242	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	cb78176f3653dddc
24	362	7776706507034272	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	30f51d844e329496
39	378	7776706507184279	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	d0886aec47ac34ed
45	387	7776706507244263	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	937bd73ff42bf221
52	397	7776706507314274	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	73d2ea14d681591b
67	407	7776706507464279	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	58f01bb682f2e3d7
19	417	7776706506984267	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	31643bd8ceda0e35
101	434	7776706507804311	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	210943843935fd9b
93	443	7776706507714284	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	5584ae294dc8bc2c
110	453	7776706507894288	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	673ea9e469617ab
75	465	7776706507544284	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	e1b94d12daf9ffd8
84	605	7776706507624303	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	cdfb41a536fb8bbd
301	635	7776706509792795	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	ca32f6349de14497
119	658	7776706507984262	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	483192b6cbcd8ea8
136	670	7776706508150886	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	497155e73f89de4d
363	691	7776706510412825	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	4bd0cfcf61b4f9eb
354	727	7776706510332804	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	c0df74edbaa8d4e2
379	737	7776706510582797	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8a2798b39b38967
158	755	7776706508370904	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	14c31d09cb487174
387	827	7776706510662802	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	83e187d5649e4953
241	837	7776706509202796	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	6aa3570ab1cd2ae2
128	858	7776706508074292	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	cfdd251618f6a714
418	911	7776706510964494	esp-idf/log/liblog.a	c91a251e1ec6f52c
434	931	7776706511132651	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4824227fe51f023e
397	940	7776706510764476	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	63f8478ca2cd3234
407	1012	7776706510864488	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	fda948f7d94318fe
670	1022	7776706513491273	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	dcae97481a03c066
636	1031	7776706513141264	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	647702da2488015a
658	1040	7776706513371266	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	83781b0edbf4dd06
755	1064	7776706514341268	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	b967448cdfaa7383
691	1096	7776706513701243	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	fc218c3d43777ba7
466	1113	7776706511451264	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	4b64013a1931ce9e
453	1158	7776706511321247	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	858d4baa72e0a17b
737	1221	7776706514161256	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	6790c1df7306deb2
605	1258	7776706512841293	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	bf5475bb65ab941c
443	1294	7776706511221259	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	142b08fee5111a88
911	1343	7776706515901263	esp-idf/esp_rom/libesp_rom.a	fa17a62755a86f30
941	1358	7776706516191255	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	4ebae100feb37f9a
728	1388	7776706514061262	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	70b39228ff3c3953
1031	1409	7776706517101280	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	abca8987946c4
1064	1419	7776706517431238	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	1b857d8603efa092
858	1432	7776706515371241	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	ffdd27642721c0bf
838	1452	7776706515161259	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	a0cddf8baf8a7725
1113	1506	7776706517921230	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	6e27940ab0c3e7c
1097	1526	7776706517751248	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	b80ca273a9dbb954
1040	1535	7776706517191241	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	889baa59c793157
1258	1594	7776706519369661	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	25b380721e1b29b6
1221	1604	7776706518999670	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	7337709c27fee1ca
827	1728	7776706515061250	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	606a76877fef8d81
1022	1770	7776706517011243	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	233500e586f9ee34
1358	1779	7776706520369641	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	be38ffb92a960c1f
1409	1792	7776706520879622	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	8f538409bc2cd6cc
1535	1800	7776706522149617	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	885af7f345ad7210
1389	1808	7776706520679640	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	28f496dae6971f53
1343	1840	7776706520219640	esp-idf/esp_common/libesp_common.a	b03fb5144ef533f3
1594	1857	7776706522729640	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	445508437b2f4d37
1158	1866	7776706518371243	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	749b6846e469439c
1604	1876	7776706522829652	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	6d24e56d1ded1fb9
1432	1889	7776706521119643	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	a1902cbe249b4406
1294	1897	7776706519729618	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	35c22edb96f4c16a
931	1906	7776706516101257	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	b77bc6b182498ad1
1526	1914	7776706522049619	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	24015bfc507f4dc0
1452	1953	7776706521309614	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	3f7cd516dfa5aa97
1728	1990	7776706524073441	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	97f79caf25082d69
1419	2030	7776706520979625	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	61d58f48e37c570c
1779	2059	7776706524583430	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	29b8fde4d883d840
1866	2069	7776706525453482	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	36a0b2d6be3ef1b
1770	2070	7776706524493440	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	1abbd206e65300d7
1898	2070	7776706525763455	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	d880b4553289daf1
1792	2073	7776706524713447	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	754ba61bf0dd9e65
1808	2088	7776706524873450	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	2fbd10801eda5f93
1800	2089	7776706524783485	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	d3b413fd7717610e
1857	2089	7776706525363445	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	9da01f23b4cebb82
1876	2116	7776706525553469	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	58accd046d586db1
1889	2117	7776706525683421	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	e9fa507b2c68099a
1915	2126	7776706525933411	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	88ce9b6940f96e5c
1906	2127	7776706525853431	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	1af531e95af75350
1013	2128	7776706516911249	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	7a3f3769d1ee15cd
2030	2140	7776706528113467	project_elf_src_esp32c3.c	6c7d774e998bc721
2030	2140	7776706528113467	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/project_elf_src_esp32c3.c	6c7d774e998bc721
1953	2150	7776706526333437	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	7ab51ff84ae785a9
1990	2157	7776706526693470	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	b9f0ed134b19d3fd
1840	2172	7776706525193430	esp-idf/esp_hw_support/libesp_hw_support.a	894c438beedadd28
2140	2215	7776706528193424	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	fadd1928e73520b8
2059	2234	7776706527383455	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	74d83039a25dbef3
2172	2268	7776706528513440	esp-idf/esp_system/libesp_system.a	4a8745f9c5d686d2
2268	2375	7776706529469684	esp-idf/efuse/libefuse.a	ee4f0e88cc352499
2375	2517	7776706530539672	esp-idf/bootloader_support/libbootloader_support.a	8733c75031909e5e
2517	2598	7776706531959682	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	dbdcbbb2f8f34b2
1506	2654	7776706521849636	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	2846994fd4688db
2598	2673	7776706532769688	esp-idf/spi_flash/libspi_flash.a	344553f8c1d7b7f4
2673	2757	7776706533519671	esp-idf/hal/libhal.a	38abfeb38a2a248d
2757	2831	7776706534359695	esp-idf/micro-ecc/libmicro-ecc.a	eed29c262133c2d0
2831	2948	7776706535099702	esp-idf/soc/libsoc.a	2ed56a05eac453da
2948	3021	7776706536269662	esp-idf/main/libmain.a	5bc613d7954c6145
3021	3153	7776706536999652	bootloader.elf	fced577b480f0e0d
3153	3448	7776706541225967	.bin_timestamp	e514e3b0f06691b1
3153	3448	7776706541225967	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/.bin_timestamp	e514e3b0f06691b1
3448	3557	7776706541275966	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
3448	3557	7776706541275966	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
